// MongoDB initialization script
db = db.getSiblingDB('microservices_db');

// Create collections
db.createCollection('users');
db.createCollection('products');

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });
db.products.createIndex({ "name": 1 });
db.products.createIndex({ "category": 1 });

// Insert sample data
db.users.insertMany([
  {
    username: "john_doe",
    email: "<EMAIL>",
    password: "$2b$10$rOzJqZxjwHqHlWlHlWlHlO", // This would be a hashed password
    firstName: "John",
    lastName: "Doe",
    role: "user",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    username: "admin",
    email: "<EMAIL>",
    password: "$2b$10$rOzJqZxjwHqHlWlHlWlHlO", // This would be a hashed password
    firstName: "Admin",
    lastName: "User",
    role: "admin",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

db.products.insertMany([
  {
    name: "Laptop",
    description: "High-performance laptop for professionals",
    price: 999.99,
    category: "Electronics",
    stock: 50,
    sku: "LAP001",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "Smartphone",
    description: "Latest smartphone with advanced features",
    price: 699.99,
    category: "Electronics",
    stock: 100,
    sku: "PHN001",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "Coffee Mug",
    description: "Ceramic coffee mug",
    price: 12.99,
    category: "Home & Kitchen",
    stock: 200,
    sku: "MUG001",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

print("Database initialized with sample data");

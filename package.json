{"name": "microservices-project", "version": "1.0.0", "description": "A microservices architecture project using Node.js, Express.js, and MongoDB", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "start:user-service": "cd services/user-service && npm start", "start:product-service": "cd services/product-service && npm start", "start:api-gateway": "cd services/api-gateway && npm start", "dev:user-service": "cd services/user-service && npm run dev", "dev:product-service": "cd services/product-service && npm run dev", "dev:api-gateway": "cd services/api-gateway && npm run dev", "install:all": "npm install && cd services/user-service && npm install && cd ../product-service && npm install && cd ../api-gateway && npm install", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build"}, "keywords": ["microservices", "nodejs", "express", "mongodb", "docker"], "author": "Your Name", "license": "MIT", "devDependencies": {"nodemon": "^3.0.1"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1"}}